<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('EmployeeController');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();

// الصفحة الرئيسية
$routes->get('/', 'EmployeeController::index');

// مسارات الموظفين
$routes->group('employee', function($routes) {
    $routes->post('upload', 'EmployeeController::handleUpload');
    $routes->get('search', 'EmployeeController::searchPage');
    $routes->post('search', 'EmployeeController::processSearch');
    $routes->get('download', 'EmployeeController::downloadData');
});