<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<style>
    /* تصغير العناصر لأخذ لقطة شاشة للجدولين معاً */
    .employee-card {
        background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        padding: 15px;
        transition: transform 0.3s ease;
    }

    .employee-card:hover {
        transform: translateY(-2px);
    }

    .info-label {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 3px;
        font-size: 0.85em;
    }

    .info-value {
        color: #34495e;
        font-size: 0.9em;
        padding: 4px 6px;
        background: rgba(255,255,255,0.7);
        border-radius: 4px;
        margin-bottom: 6px;
    }

    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        margin-bottom: 15px;
        overflow: hidden;
    }

    .table-title {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        padding: 8px 12px;
        font-size: 1em;
        font-weight: 600;
        text-align: center;
        margin-bottom: 0;
    }

    .custom-table {
        margin-bottom: 0;
        font-size: 0.8em;
    }

    .custom-table thead th {
        background: #f8f9fa;
        color: #2c3e50;
        font-weight: 600;
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        padding: 6px 4px;
        font-size: 0.75em;
    }

    .custom-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .custom-table td {
        vertical-align: middle;
        text-align: center;
        padding: 4px 3px;
        font-size: 0.75em;
    }

    .percentage-badge {
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.7em;
    }

    .product-name {
        font-weight: 700;
        color: #1a237e;
        padding: 4px 8px;
        border-radius: 4px;
        background: #f8f9fa;
        display: inline-block;
        min-width: 120px;
        text-align: center;
        border: 1px solid #e3e6f0;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        font-size: 0.7em;
    }

    .product-name:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background: #fff;
    }

    .search-form {
        background: white;
        padding: 12px;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        margin-bottom: 15px;
    }

    .btn-download {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border: none;
        padding: 6px 15px;
        border-radius: 15px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        font-size: 0.85em;
    }

    .btn-download:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(46,204,113,0.3);
        color: white;
    }

    .stats-summary {
        display: flex;
        justify-content: space-around;
        margin-bottom: 15px;
    }

    .stat-item {
        text-align: center;
        padding: 8px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        flex: 1;
        margin: 0 5px;
    }

    .stat-value {
        font-size: 1.2em;
        font-weight: 700;
        color: #2c3e50;
    }

    .stat-label {
        color: #7f8c8d;
        margin-top: 3px;
        font-size: 0.8em;
    }

    /* تصغير الحاوي الرئيسي */
    .container {
        max-width: 1400px;
    }

    /* تصغير المسافات العامة */
    .py-4 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
</style>

<div class="container py-4">
    <!-- نموذج البحث -->
    <div class="search-form">
        <form action="<?= base_url('employee/search') ?>" method="post" class="row g-3 align-items-center">
            <div class="col-md-8">
                <input type="text" name="search_term" class="form-control form-control-lg" 
                       placeholder="ابحث باستخدام الاسم، رقم الهوية، أو الرقم الوظيفي" 
                       value="<?= esc($search_term_value ?? '') ?>">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary btn-lg w-100">بحث</button>
            </div>
        </form>
    </div>

    <?php if (isset($search_error)): ?>
        <div class="alert alert-danger" role="alert">
            <?= esc($search_error) ?>
        </div>
    <?php endif; ?>

    <?php if (isset($employee_data) && !empty($employee_data)): ?>
        <!-- معلومات الموظف -->
        <div class="employee-card">
            <div class="row">
                <div class="col-md-4">
                    <div class="info-label">الاسم</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['name']) ?></div>
                    
                    <div class="info-label">رقم الهوية</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['national_id']) ?></div>
                    
                    <div class="info-label">الرقم الوظيفي</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['emp_id']) ?></div>
                </div>
                <div class="col-md-4">
                    <div class="info-label">المنصب</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['position']) ?></div>
                    
                    <div class="info-label">المنطقة</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['region']) ?></div>
                    
                    <div class="info-label">الفرع</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['branch_name']) ?></div>
                </div>
                <div class="col-md-4">
                    <div class="info-label">كود الفرع</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['branch_code']) ?></div>
                    
                    <div class="info-label">ملاحظات</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['note']) ?></div>
                    
                    <div class="info-label">نسبة الخصم</div>
                    <div class="info-value"><?= esc($employee_data['employee_info']['discount_percentage']) ?></div>
                </div>
            </div>
        </div>

        <!-- الجدول الأول -->
        <?php if (!empty($employee_data['table1'])): ?>
            <div class="table-container">
                <h3 class="table-title">جدول المبيعات مع المبيعات الرقمية</h3>
                <div class="table-responsive">
                    <table class="table custom-table">
                        <thead>
                            <tr>
                                <th>البيانات</th>
                                <th>TGT</th>
                                <th>Branch Sales</th>
                                <th>Digital Sales</th>
                                <th>Total</th>
                                <th>المتبقي</th>
                                <th>نسبة الإنجاز</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($employee_data['table1'] as $item): ?>
                                <tr>
                                    <td><span class="product-name"><?= esc($item['label']) ?></span></td>
                                    <td><?= number_format($item['tgt']) ?></td>
                                    <td><?= number_format($item['branch_sales']) ?></td>
                                    <td><?= number_format($item['digital_sales']) ?></td>
                                    <td><strong><?= number_format($item['total']) ?></strong></td>
                                    <td><?= number_format($item['remaining']) ?></td>
                                    <td>
                                        <?php
                                        $percentage = (float)str_replace('%', '', $item['percentage']);
                                        $colorClass = 'bg-danger text-white';
                                        if ($percentage >= 90) {
                                            $colorClass = 'bg-success text-white';
                                        } elseif ($percentage >= 70) {
                                            $colorClass = 'bg-info text-white';
                                        } elseif ($percentage >= 50) {
                                            $colorClass = 'bg-warning text-dark';
                                        }
                                        ?>
                                        <span class="percentage-badge <?= $colorClass ?>"><?= $item['percentage'] ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- الجدول الثاني -->
        <?php if (!empty($employee_data['table2'])): ?>
            <div class="table-container">
                <h3 class="table-title">جدول المبيعات بدون مبيعات رقمية</h3>
                <div class="table-responsive">
                    <table class="table custom-table">
                        <thead>
                            <tr>
                                <th>البيانات</th>
                                <th>TGT</th>
                                <th>Sales</th>
                                <th>المتبقي</th>
                                <th>نسبة الإنجاز</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($employee_data['table2'] as $item): ?>
                                <tr>
                                    <td><span class="product-name"><?= esc($item['label']) ?></span></td>
                                    <td><?= number_format($item['tgt']) ?></td>
                                    <td><?= number_format($item['sales']) ?></td>
                                    <td><?= number_format($item['remaining']) ?></td>
                                    <td>
                                        <?php
                                        $percentage = (float)str_replace('%', '', $item['percentage']);
                                        $colorClass = 'bg-danger text-white';
                                        if ($percentage >= 90) {
                                            $colorClass = 'bg-success text-white';
                                        } elseif ($percentage >= 70) {
                                            $colorClass = 'bg-info text-white';
                                        } elseif ($percentage >= 50) {
                                            $colorClass = 'bg-warning text-dark';
                                        }
                                        ?>
                                        <span class="percentage-badge <?= $colorClass ?>"><?= $item['percentage'] ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- زر التحميل -->
        <div class="text-center mt-4">
            <a href="<?= base_url('employee/download') ?>" class="btn btn-download">
                <i class="fas fa-download me-2"></i> تحميل البيانات
            </a>
        </div>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>