<?= view('templates/header', ['title' => 'رفع ملف الموظفين']) ?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                رفع ملف بيانات الموظفين (xlsx, xls)
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('upload_error')): ?>
                    <div class="alert alert-danger"><?= session()->getFlashdata('upload_error') ?></div>
                <?php endif; ?>
                <?php if (session()->getFlashdata('upload_success')): ?>
                    <div class="alert alert-success"><?= session()->getFlashdata('upload_success') ?></div>
                <?php endif; ?>

                <?= form_open_multipart('employee/upload', ['class' => 'mt-3']) ?>
                    <div class="mb-3">
                        <label for="userfile" class="form-label">اختر ملف xlsx, xls:</label>
                        <!-- <input type="file" name="userfile" id="userfile" class="form-control" required accept=".csv"> -->
                        <input type="file" name="userfile" id="userfile" class="form-control" required 
                        accept=".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        <div class="form-text">يجب أن يكون الملف بصيغة xlsx, xls ومشابهًا في الهيكل للملف المثال.</div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">رفع الملف</button>
                <?= form_close() ?>

                <?php if (!empty($file_uploaded_name)): ?>
                    <hr>
                    <p class="text-center">الملف المرفوع حالياً: <strong><?= esc($file_uploaded_name) ?></strong></p>
                    <div class="text-center">
                         <a href="<?= site_url('employee/search') ?>" class="btn btn-info">الانتقال إلى صفحة البحث</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= view('templates/footer') ?>