<?php
// employee_search_page.php

$file_uploaded_name = $file_uploaded_name ?? session()->get('uploaded_file_name') ?? 'غير محدد';
$search_error = $search_error ?? session()->getFlashdata('search_error') ?? null;
// $search_term_value يتم تمريره من المتحكم لإعادة ملء الحقل
$search_term_value = $search_term_value ?? ''; 
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث عن موظف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f4f6f9;
        }
        .container-fluid { padding-left: 0; padding-right: 0; }
        .content-wrapper { padding: 20px; display: flex; justify-content: center; align-items: center; min-height: 80vh; }
         .card {
            border-radius: .75rem;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px; /* تحديد عرض أقصى للبطاقة */
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            border-top-left-radius: .75rem;
            border-top-right-radius: .75rem;
            padding: 1rem 1.25rem;
        }
         .card-title { margin-bottom: 0; }
    </style>
</head>
<body>

<?= view('templates/header', ['title' => 'البحث عن موظف']) ?> <div class="content-wrapper">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                 <div class="card search-card">
                    <div class="card-header">
                        <h5 class="card-title">البحث عن موظف</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($file_uploaded_name) && $file_uploaded_name !== 'غير محدد'): ?>
                            <p class="text-muted small">الملف المرفوع حاليًا: <strong><?= esc($file_uploaded_name) ?></strong></p>
                        <?php endif; ?>

                        <?php if (isset($search_error) && $search_error): ?>
                            <div class="alert alert-danger" role="alert">
                                <?= esc($search_error) ?>
                            </div>
                        <?php endif; ?>
                        <?php if (session()->getFlashdata('upload_success')): ?>
                             <div class="alert alert-success"><?= session()->getFlashdata('upload_success') ?></div>
                        <?php endif; ?>
                        <?php if (session()->getFlashdata('upload_error') && !isset($search_error)): // عرض خطأ الرفع فقط إذا لم يكن هناك خطأ بحث ?>
                             <div class="alert alert-warning"><?= session()->getFlashdata('upload_error') ?></div>
                        <?php endif; ?>


                        <form action="<?= site_url('employee/search') ?>" method="post">
                            <?= csrf_field() ?>
                            <div class="mb-3">
                                <label for="search_term" class="form-label fw-bold">أدخل رقم الهوية، الاسم، أو الرقم الوظيفي:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-lg" 
                                           id="search_term" name="search_term" 
                                           value="<?= esc($search_term_value ?? '') ?>" 
                                           placeholder="مثال: 280..." required>
                                    <button class="btn btn-primary btn-lg" type="submit">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-search" viewBox="0 0 16 16"><path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001q.044.06.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1 1 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0"/></svg>
                                        بحث
                                    </button>
                                </div>
                                <div class="form-text mt-2">
                                    يمكنك البحث باستخدام أحد المعايير المذكورة.
                                </div>
                            </div>
                        </form>
                        <hr>
                         <div class="text-center mt-3">
                            <a href="<?= site_url('/') ?>" class="btn btn-outline-secondary">رفع ملف جديد</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= view('templates/footer') ?> <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>