<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet; // غير مستخدمة مباشرة، يمكن إزالتها إذا لم تنشئ ملفات Excel
use PhpOffice\PhpSpreadsheet\Reader\Exception as SpreadsheetException;

class EmployeeController extends BaseController
{
    protected $helpers = ['form', 'url'];

    public function index()
    {
        $session = session();
        $data = [
            'upload_error' => $session->getFlashdata('upload_error'),
            'file_uploaded_name' => $session->get('uploaded_file_name')
        ];
        return view('employee/upload_form', $data);
    }

    public function handleUpload()
    {
        $session = session();
        $file = $this->request->getFile('userfile');

        if ($file && $file->isValid() && !$file->hasMoved()) {
            $originalName = $file->getClientName();
            $extension = strtolower($file->getClientExtension());

            if (!in_array($extension, ['xlsx', 'xls'])) {
                $session->setFlashdata('upload_error', 'يرجى رفع ملف Excel صالح (امتداد .xlsx أو .xls). الامتداد المكتشف: ' . esc($extension));
                return redirect()->to('/');
            }
            
            $newName = $file->getRandomName(); 
            
            if ($file->move(WRITEPATH . 'uploads', $newName)) {
                $session->set('uploaded_file_path', WRITEPATH . 'uploads/' . $newName);
                $session->set('uploaded_file_name', $originalName); 
                $session->setFlashdata('upload_success', 'تم رفع الملف بنجاح: ' . esc($originalName));
                
                $session->remove('employee_data');
                $session->remove('search_term'); // مسح مصطلح البحث القديم
                
                return redirect()->to('/employee/search'); 
            } else {
                $session->setFlashdata('upload_error', 'حدث خطأ أثناء نقل الملف.');
            }
        } else {
            $errorMessage = 'لم يتم اختيار ملف أو الملف غير صالح.';
            if ($file && $file->getErrorString() && $file->getError()) { 
                $errorMessage .= ' السبب: ' . $file->getErrorString() . '(' . $file->getError() . ')';
            }
            $session->setFlashdata('upload_error', $errorMessage);
        }
        
        return redirect()->to('/'); 
    }
    
    public function searchPage()
    {
        $session = session();
        if (!$session->get('uploaded_file_path')) {
            $session->setFlashdata('upload_error', 'يرجى رفع ملف Excel أولاً.');
            return redirect()->to('/');
        }

        // استرجاع مصطلح البحث لملء الحقل إذا كان البحث سابقًا غير ناجح
        $searchTerm = $session->getFlashdata('search_term_repopulate') ?? $session->get('search_term_repopulate');
        if($searchTerm === null && $this->request->getPost('search_term')){ // إذا كان أول بحث
            $searchTerm = trim((string) $this->request->getPost('search_term'));
        }


        $data = [
            'employee_data' => $session->get('employee_data'), // للحفاظ على بيانات الموظف المعروض
            'search_error' => $session->getFlashdata('search_error'),
            'search_term_value' => $searchTerm, // لإعادة ملء حقل البحث
            'file_uploaded_name' => $session->get('uploaded_file_name') ?? 'الملف المرفوع'
        ];

        // إذا تم العثور على بيانات موظف (من عملية بحث سابقة ناجحة)، اعرض صفحة التفاصيل
        // صفحة التفاصيل يجب أن تحتوي أيضًا على نموذج البحث
        if ($data['employee_data']) {
            return view('employee/employee_details', $data);
        }

        // إذا لم يكن هناك بيانات موظف، اعرض صفحة البحث العادية (نموذج فارغ)
        return view('employee/search_page', $data);
    }

    public function processSearch()
    {
        $session = session();
        $filePath = $session->get('uploaded_file_path');
        $searchTerm = trim((string) $this->request->getPost('search_term'));
        
        // تخزين مصطلح البحث لإعادة ملء الحقل في حالة الخطأ أو عدم العثور
        $session->set('search_term_repopulate', $searchTerm);

        if (empty($searchTerm)) {
            $session->setFlashdata('search_error', 'يرجى إدخال نص للبحث.');
            $session->remove('employee_data');
            return redirect()->to('/employee/search');
        }

        if (!$filePath || !file_exists($filePath)) {
            $session->setFlashdata('upload_error', 'ملف البيانات غير موجود. يرجى إعادة رفع الملف.');
            $session->remove(['uploaded_file_path', 'uploaded_file_name', 'employee_data']);
            return redirect()->to('/');
        }

        try {
            $spreadsheet = IOFactory::load($filePath);
            $sheet = $spreadsheet->getActiveSheet();
            
            // أعمدة البحث الأساسية
            $empIdCol = 'A';         // EMP ID
            $empNationalIdCol = 'C';  // EMP National ID
            $empNameCol = 'D';        // EMP Name
            $cbuUserCol = 'G';        // CBU User

            $found = false;
            $employeeData = null;
            
            $highestRow = $sheet->getHighestDataRow();
            $firstDataRow = 2; // نفترض أن البيانات تبدأ من الصف الثاني بعد العناوين

            for ($row = $firstDataRow; $row <= $highestRow; $row++) {
                $currentEmpId = trim((string)$sheet->getCell($empIdCol . $row)->getValue());
                $currentNationalId = trim((string)$sheet->getCell($empNationalIdCol . $row)->getValue());
                $currentName = trim((string)$sheet->getCell($empNameCol . $row)->getValue());
                $currentCbuUser = trim((string)$sheet->getCell($cbuUserCol . $row)->getValue());

                if (strcasecmp($currentNationalId, $searchTerm) == 0 ||
                    mb_stripos($currentName, $searchTerm) !== false ||
                    strcasecmp($currentCbuUser, $searchTerm) == 0 ||
                    strcasecmp($currentEmpId, $searchTerm) == 0) {
                    
                    $employeeData = [
                        'employee_info' => [
                            'name' => $currentName,
                            'national_id' => $currentNationalId,
                            'cbu_user' => $currentCbuUser,
                            'emp_id' => (string) $sheet->getCell('A' . $row)->getValue(),
                            'supervisor_id' => (string) $sheet->getCell('B' . $row)->getValue(),
                            'position' => (string) $sheet->getCell('E' . $row)->getValue(),
                            'position_united' => (string) $sheet->getCell('F' . $row)->getValue(),
                            'ebu_user' => (string) $sheet->getCell('H' . $row)->getValue(),
                            'region' => (string) $sheet->getCell('I' . $row)->getValue(),
                            'area' => (string) $sheet->getCell('J' . $row)->getValue(),
                            'branch_name' => (string) $sheet->getCell('K' . $row)->getValue(),
                            'branch_code' => (string) $sheet->getCell('L' . $row)->getValue(),
                            'note' => (string) $sheet->getCell('M' . $row)->getValue(),
                            'vacation_days' => (string) $sheet->getCell('N' . $row)->getValue(),
                            'discount_percentage' => (string) $sheet->getCell('O' . $row)->getValue()
                        ],
                        'table1' => [],
                        'table2' => []
                    ];

                    // تعريف عناصر الجدول الأول
                    $table1Items = [
                        'jawwy' => ['label' => 'Jawwy (QTY)', 'tgt' => 'AV', 'branch_sales' => 'AW', 'digital_sales' => 'AX', 'total' => 'AY', 'percentage' => 'AZ'],
                        'residential_fwa' => ['label' => 'Residential FWA (QTY)', 'tgt' => 'BA', 'branch_sales' => 'BB', 'digital_sales' => 'BC', 'total' => 'BD', 'percentage' => 'BE'],
                        'ios_watch' => ['label' => 'IOS Watch', 'tgt' => 'CB', 'branch_sales' => 'CC', 'digital_sales' => 'CD', 'total' => 'CE', 'percentage' => 'CF'],
                        'ios_airpods' => ['label' => 'IOS AirPods', 'tgt' => 'BW', 'branch_sales' => 'BX', 'digital_sales' => 'BY', 'total' => 'BZ', 'percentage' => 'CA'],
                        'noniphone' => ['label' => 'NoniPhone (SAR)', 'tgt' => 'U', 'branch_sales' => 'V', 'digital_sales' => 'W', 'total' => 'X', 'percentage' => 'Y'],
                        'pp_data' => ['label' => 'PP Data (QTY)', 'tgt' => 'AQ', 'branch_sales' => 'AR', 'digital_sales' => 'AS', 'total' => 'AT', 'percentage' => 'AU'],
                        'iphone' => ['label' => 'iPhone (SAR)', 'tgt' => 'P', 'branch_sales' => 'Q', 'digital_sales' => 'R', 'total' => 'S', 'percentage' => 'T'],
                        'pp_voice' => ['label' => 'PP Voice (QTY)', 'tgt' => 'AL', 'branch_sales' => 'AM', 'digital_sales' => 'AN', 'total' => 'AO', 'percentage' => 'AP'],
                        'ios_cases' => ['label' => 'IOS Cases (QTY)', 'tgt' => 'BL', 'branch_sales' => 'BM', 'digital_sales' => 'BN', 'total' => 'BO', 'percentage' => 'BP'],
                        'samsung_accessories' => ['label' => 'Samsung Accessories', 'tgt' => 'CL', 'branch_sales' => 'CM', 'digital_sales' => 'CN', 'total' => 'CO', 'percentage' => 'CP'],
                        'growth_accessories' => ['label' => 'Growth Accessories', 'tgt' => 'CG', 'branch_sales' => 'CH', 'digital_sales' => 'CI', 'total' => 'CJ', 'percentage' => 'CK']
                    ];

                    foreach ($table1Items as $key => $itemDetails) {
                        $tgt = $this->getNumericValue($this->getCellValue($sheet, $itemDetails['tgt'], $row));
                        $branchSales = $this->getNumericValue($this->getCellValue($sheet, $itemDetails['branch_sales'], $row));
                        $digitalSales = $this->getNumericValue($this->getCellValue($sheet, $itemDetails['digital_sales'], $row));
                        
                        $branchSales = is_numeric($branchSales) ? (float)$branchSales : 0;
                        $digitalSales = is_numeric($digitalSales) ? (float)$digitalSales : 0;
                        
                        $total = $branchSales + $digitalSales;
                        
                        $calculatedPercentage = 0;
                        if ($tgt > 0) {
                            $calculatedPercentage = round(($total / $tgt) * 100, 0);
                        } elseif ($total > 0) {
                            $calculatedPercentage = 100;
                        }
                        
                        $employeeData['table1'][$key] = [
                            'label' => $itemDetails['label'],
                            'tgt' => $tgt,
                            'branch_sales' => $branchSales,
                            'digital_sales' => $digitalSales,
                            'total' => $total,
                            'remaining' => $tgt - $total,
                            'percentage' => $calculatedPercentage . '%'
                        ];
                    }

                    // تعريف عناصر الجدول الثاني
                    $table2Items = [
                        'non_mrc' => ['label' => 'Non-MRC (SAR)', 'tgt' => 'Z', 'sales' => 'AA', 'percentage' => 'AB'],
                        'ps_data' => ['label' => 'PS Data (QTY)', 'tgt' => 'AI', 'sales' => 'AJ', 'percentage' => 'AK'],
                        'ebu_mrc' => ['label' => 'EBU MRC (SAR)', 'tgt' => 'AC', 'sales' => 'AD', 'percentage' => 'AE'],
                        'attachment_rate' => ['label' => 'Attachment rate', 'tgt' => 'BT', 'sales' => 'BU', 'percentage' => 'BV'],
                        'trade_in' => ['label' => 'Trade in', 'tgt' => 'BI', 'sales' => 'BJ', 'percentage' => 'BK'],
                        'soho' => ['label' => 'Soho (SAR)', 'tgt' => 'BQ', 'sales' => 'BR', 'percentage' => 'BS'],
                        'ps_voice' => ['label' => 'PS Voice (QTY)', 'tgt' => 'AF', 'sales' => 'AG', 'percentage' => 'AH'],
                        'residential_ftth' => ['label' => 'Residential FTTH (QTY)', 'tgt' => 'BF', 'sales' => 'BG', 'percentage' => 'BH'],
                        'device_protection' => ['label' => 'Device Protection QTY', 'tgt' => 'CQ', 'sales' => 'CR', 'percentage' => 'CS']
                    ];

                    foreach ($table2Items as $key => $itemDetails) {
                        $tgt = $this->getNumericValue($this->getCellValue($sheet, $itemDetails['tgt'], $row));
                        $sales = $this->getNumericValue($this->getCellValue($sheet, $itemDetails['sales'], $row));
                        
                        // الحصول على النسبة المئوية المحسوبة مباشرة من الخلية
                        $percentageCell = $sheet->getCell($itemDetails['percentage'] . $row);
                        $percentageValue = $percentageCell->isFormula() ? 
                            $percentageCell->getCalculatedValue() : 
                            $percentageCell->getValue();
                        
                        $calculatedPercentage = $tgt > 0 ? round(($sales / $tgt) * 100, 0) : 0;
                        
                        // استخدام القيمة المحسوبة من Excel إذا كانت متوفرة
                        if (is_numeric($percentageValue)) {
                            $displayPercentage = round($percentageValue * 100, 0) . '%';
                        } else {
                            $displayPercentage = $calculatedPercentage . '%';
                        }
                        
                        if ($tgt == 0 && $sales > 0) $displayPercentage = "100%";
                        if ($tgt == 0 && $sales == 0) $displayPercentage = "0%";

                        $employeeData['table2'][$key] = [
                            'label' => $itemDetails['label'],
                            'tgt' => $tgt,
                            'sales' => $sales,
                            'remaining' => $tgt - $sales,
                            'percentage' => $displayPercentage
                        ];
                    }

                    // تحويل نسبة الخصم
                    if (isset($employeeData['employee_info']['discount_percentage'])) {
                        $discount = str_replace('%', '', $employeeData['employee_info']['discount_percentage']);
                        $discount = floatval($discount) * 100;
                        $employeeData['employee_info']['discount_percentage'] = $discount . '%';
                    }

                    // ترتيب بيانات الجدول الأول تصاعدياً حسب النسبة المئوية
                    if (!empty($employeeData['table1'])) {
                        uasort($employeeData['table1'], function($a, $b) {
                            $percentA = (float)str_replace('%', '', $a['percentage']);
                            $percentB = (float)str_replace('%', '', $b['percentage']);
                            return $percentA - $percentB;
                        });
                    }

                    // ترتيب بيانات الجدول الثاني تصاعدياً حسب النسبة المئوية
                    if (!empty($employeeData['table2'])) {
                        uasort($employeeData['table2'], function($a, $b) {
                            $percentA = (float)str_replace('%', '', $a['percentage']);
                            $percentB = (float)str_replace('%', '', $b['percentage']);
                            return $percentA - $percentB;
                        });
                    }

                    $found = true;
                    break;
                }
            }

            if ($found) {
                $session->set('employee_data', $employeeData);
                $session->remove('search_term_repopulate');
            } else {
                $session->setFlashdata('search_error', 'لم يتم العثور على موظف بالمعلومات المقدمة (رقم الموظف، رقم الهوية، الاسم، أو كود المستخدم): ' . esc($searchTerm));
                $session->remove('employee_data');
            }

        } catch (SpreadsheetException $e) {
            $session->setFlashdata('search_error', 'خطأ في قراءة ملف Excel: ' . esc($e->getMessage()));
            log_message('error', '[SpreadsheetException] ' . $e->getMessage() . ' Trace: ' . $e->getTraceAsString());
        } catch (\Exception $e) {
            $session->setFlashdata('search_error', 'حدث خطأ عام أثناء البحث: ' . esc($e->getMessage()));
            log_message('error', '[Exception] ' . $e->getMessage() . ' Trace: ' . $e->getTraceAsString());
        }
        
        return redirect()->to('/employee/search');
    }

    private function getNumericValue($value) {
        if ($value === null || $value === '') return 0;
        if (is_numeric($value)) return (float)$value;
        
        // إزالة أي رموز عملة أو فواصل آلاف أو مسافات
        $cleanedValue = preg_replace('/[^\d.-]/', '', $value);
        
        return is_numeric($cleanedValue) ? (float)$cleanedValue : 0;
    }

    private function getCellValue($sheet, $cell, $row) {
        $cellObj = $sheet->getCell($cell . $row);
        
        // الحصول على القيمة المحسوبة إذا كانت الخلية تحتوي على صيغة
        if ($cellObj->isFormula()) {
            try {
                return $cellObj->getCalculatedValue();
            } catch (\Exception $e) {
                log_message('error', 'Error calculating formula for cell ' . $cell . $row . ': ' . $e->getMessage());
                return 0;
            }
        }
        
        return $cellObj->getValue();
    }

    public function downloadData()
    {
        $session = session();
        $employeeData = $session->get('employee_data');

        if (!$employeeData || !isset($employeeData['employee_info'])) {
            $session->setFlashdata('search_error', 'لا توجد بيانات موظف لتحميلها. يرجى البحث أولاً.');
            return redirect()->to('/employee/search');
        }

        // إنشاء ملف Excel جديد
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // تعيين اتجاه الورقة من اليمين إلى اليسار
        $sheet->setRightToLeft(true);

        // تنسيق العنوان
        $sheet->mergeCells('A1:O1');
        $sheet->setCellValue('A1', 'تقرير أداء الموظف');
        $sheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16],
            'alignment' => ['horizontal' => 'center']
        ]);
        
        // معلومات الموظف
        $sheet->setCellValue('A3', '━━━━━━━━━━ معلومات الموظف ━━━━━━━━━━');
        $sheet->mergeCells('A3:O3');
        $sheet->getStyle('A3')->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => 'center']
        ]);

        // عناوين معلومات الموظف
        $infoHeaders = [
            '👤 الاسم', '🆔 رقم الهوية', '👨‍💼 رقم الموظف', '👥 المشرف', '💼 المنصب',
            '📋 المنصب الموحد', '🏢 مستخدم CBU', '🏭 مستخدم EBU', '🌍 المنطقة', '🏙️ المدينة',
            '🏬 اسم الفرع', '🔢 كود الفرع', '📝 ملاحظات', '📅 أيام الإجازة', '💰 نسبة الخصم'
        ];
        $sheet->fromArray($infoHeaders, null, 'A4');
        
        // بيانات الموظف
        $infoData = [
            $employeeData['employee_info']['name'] ?? '',
            $employeeData['employee_info']['national_id'] ?? '',
            $employeeData['employee_info']['emp_id'] ?? '',
            $employeeData['employee_info']['supervisor_id'] ?? '',
            $employeeData['employee_info']['position'] ?? '',
            $employeeData['employee_info']['position_united'] ?? '',
            $employeeData['employee_info']['cbu_user'] ?? '',
            $employeeData['employee_info']['ebu_user'] ?? '',
            $employeeData['employee_info']['region'] ?? '',
            $employeeData['employee_info']['area'] ?? '',
            $employeeData['employee_info']['branch_name'] ?? '',
            $employeeData['employee_info']['branch_code'] ?? '',
            $employeeData['employee_info']['note'] ?? '',
            $employeeData['employee_info']['vacation_days'] ?? '',
            $employeeData['employee_info']['discount_percentage'] ?? '0%'
        ];
        $sheet->fromArray($infoData, null, 'A5');

        // تنسيق خلايا معلومات الموظف
        $sheet->getStyle('A4:O5')->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => 'thin']
            ]
        ]);
        $sheet->getStyle('A4:O4')->applyFromArray([
            'fill' => [
                'fillType' => 'solid',
                'startColor' => ['rgb' => 'E2EFDA']
            ],
            'font' => ['bold' => true]
        ]);

        // الجدول الأول
        if (isset($employeeData['table1']) && !empty($employeeData['table1'])) {
            $currentRow = 7;
            
            // عنوان الجدول الأول
            $sheet->mergeCells("A{$currentRow}:G{$currentRow}");
            $sheet->setCellValue("A{$currentRow}", '━━━━━━━━━━ جدول المبيعات مع المبيعات الرقمية ━━━━━━━━━━');
            $sheet->getStyle("A{$currentRow}")->applyFromArray([
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => 'center']
            ]);
            
            $currentRow++;
            
            // عناوين الجدول الأول
            $table1Headers = [
                '📊 المنتج/الخدمة',
                '🎯 الهدف',
                '🏬 مبيعات الفرع',
                '💻 المبيعات الرقمية',
                '📈 الإجمالي',
                '⭐ المتبقي',
                '📊 نسبة الإنجاز'
            ];
            $sheet->fromArray($table1Headers, null, "A{$currentRow}");
            
            // تنسيق عناوين الجدول
            $sheet->getStyle("A{$currentRow}:G{$currentRow}")->applyFromArray([
                'fill' => [
                    'fillType' => 'solid',
                    'startColor' => ['rgb' => 'BDD7EE']
                ],
                'font' => ['bold' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => 'thin']
                ]
            ]);
            
            $currentRow++;
            
            // ترتيب البيانات حسب النسبة المئوية
            $table1Data = $employeeData['table1'];
            usort($table1Data, function($a, $b) {
                $percentA = (float)str_replace('%', '', $a['percentage']);
                $percentB = (float)str_replace('%', '', $b['percentage']);
                return $percentB <=> $percentA;
            });
            
            // إضافة بيانات الجدول الأول
            foreach ($table1Data as $item) {
                $rowData = [
                    '【' . ($item['label'] ?? '') . '】',
                    $item['tgt'] ?? 0,
                    $item['branch_sales'] ?? 0,
                    $item['digital_sales'] ?? 0,
                    $item['total'] ?? 0,
                    $item['remaining'] ?? 0,
                    '▶ ' . ($item['percentage'] ?? '0%')
                ];
                $sheet->fromArray($rowData, null, "A{$currentRow}");
                
                // تنسيق الصف
                $sheet->getStyle("A{$currentRow}:G{$currentRow}")->applyFromArray([
                    'borders' => [
                        'allBorders' => ['borderStyle' => 'thin']
                    ]
                ]);
                
                // تنسيق الأرقام
                $sheet->getStyle("B{$currentRow}:F{$currentRow}")->getNumberFormat()
                    ->setFormatCode('#,##0');
                
                $currentRow++;
            }
            
            $currentRow += 2;
        }

        // الجدول الثاني
        if (isset($employeeData['table2']) && !empty($employeeData['table2'])) {
            // عنوان الجدول الثاني
            $sheet->mergeCells("A{$currentRow}:E{$currentRow}");
            $sheet->setCellValue("A{$currentRow}", '━━━━━━━━━━ جدول المبيعات بدون مبيعات رقمية ━━━━━━━━━━');
            $sheet->getStyle("A{$currentRow}")->applyFromArray([
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => 'center']
            ]);
            
            $currentRow++;
            
            // عناوين الجدول الثاني
            $table2Headers = [
                '📊 المنتج/الخدمة',
                '🎯 الهدف',
                '💰 المبيعات',
                '⭐ المتبقي',
                '📊 نسبة الإنجاز'
            ];
            $sheet->fromArray($table2Headers, null, "A{$currentRow}");
            
            // تنسيق عناوين الجدول
            $sheet->getStyle("A{$currentRow}:E{$currentRow}")->applyFromArray([
                'fill' => [
                    'fillType' => 'solid',
                    'startColor' => ['rgb' => 'BDD7EE']
                ],
                'font' => ['bold' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => 'thin']
                ]
            ]);
            
            $currentRow++;
            
            // ترتيب البيانات حسب النسبة المئوية
            $table2Data = $employeeData['table2'];
            usort($table2Data, function($a, $b) {
                $percentA = (float)str_replace('%', '', $a['percentage']);
                $percentB = (float)str_replace('%', '', $b['percentage']);
                return $percentB <=> $percentA;
            });
            
            // إضافة بيانات الجدول الثاني
            foreach ($table2Data as $item) {
                $rowData = [
                    '【' . ($item['label'] ?? '') . '】',
                    $item['tgt'] ?? 0,
                    $item['sales'] ?? 0,
                    $item['remaining'] ?? 0,
                    '▶ ' . ($item['percentage'] ?? '0%')
                ];
                $sheet->fromArray($rowData, null, "A{$currentRow}");
                
                // تنسيق الصف
                $sheet->getStyle("A{$currentRow}:E{$currentRow}")->applyFromArray([
                    'borders' => [
                        'allBorders' => ['borderStyle' => 'thin']
                    ]
                ]);
                
                // تنسيق الأرقام
                $sheet->getStyle("B{$currentRow}:D{$currentRow}")->getNumberFormat()
                    ->setFormatCode('#,##0');
                
                $currentRow++;
            }
        }

        // تعيين عرض الأعمدة تلقائياً
        foreach (range('A', 'O') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // إضافة تذييل التقرير
        $currentRow += 2;
        $sheet->mergeCells("A{$currentRow}:O{$currentRow}");
        $sheet->setCellValue("A{$currentRow}", '━━━━━━━━━━ نهاية التقرير ━━━━━━━━━━');
        $sheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => 'center']
        ]);

        // تحديد اسم الملف
        $nationalId = $employeeData['employee_info']['national_id'] ?? 'data';
        $fileName = "employee_data_" . preg_replace('/[^a-z0-9_]/i', '', $nationalId) . ".xlsx";

        // إعداد headers لتحميل الملف
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');

        // إنشاء الملف وإرساله للتحميل
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }
}