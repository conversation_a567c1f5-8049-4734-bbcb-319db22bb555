#!/usr/bin/env python3
import pandas as pd

# إنشاء بيانات تجريبية
data = {
    'emp_id': ['E001', 'E002', 'E003'],
    'national_id': ['1234567890', '0987654321', '1122334455'],
    'name': ['أحمد محمد', 'فاطمة علي', 'محمد أحمد'],
    'position': ['مدير مبيعات', 'موظف مبيعات', 'مشرف مبيعات'],
    'region': ['الرياض', 'جدة', 'الدمام'],
    'branch_name': ['فرع الرياض الرئيسي', 'فرع جدة', 'فرع الدمام'],
    'branch_code': ['RYD001', 'JED001', 'DAM001'],
    'note': ['ممتاز', 'جيد', 'جيد جداً'],
    'discount_percentage': ['5%', '3%', '4%'],
    'cbu_user': ['ahmed.m', 'fatima.a', 'mohamed.a'],
    # بيانات الجدول الأول
    'product1_tgt': [1000, 800, 900],
    'product1_branch_sales': [850, 700, 750],
    'product1_digital_sales': [100, 80, 90],
    'product2_tgt': [500, 400, 450],
    'product2_branch_sales': [450, 350, 400],
    'product2_digital_sales': [50, 40, 45],
    # بيانات الجدول الثاني
    'service1_tgt': [200, 150, 180],
    'service1_sales': [180, 140, 160],
    'service2_tgt': [300, 250, 280],
    'service2_sales': [270, 230, 250]
}

df = pd.DataFrame(data)
df.to_excel('test_employee_data.xlsx', index=False)
print("تم إنشاء ملف Excel تجريبي بنجاح")
