<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Filters language settings
return [
    'noFilter'           => '"{0}" filter must have a matching alias defined.',
    'incorrectInterface' => '"{0}" must implement CodeIgniter\Filters\FilterInterface.',
];
