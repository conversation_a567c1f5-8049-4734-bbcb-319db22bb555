<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Encryption language settings
return [
    'noDriverRequested'    => 'No driver requested; Miss <PERSON> will be so upset!',
    'noHandlerAvailable'   => 'Unable to find an available "{0}" encryption handler.',
    'unKnownHandler'       => '"{0}" cannot be configured.',
    'starterKeyNeeded'     => 'Encrypter needs a starter key.',
    'authenticationFailed' => 'Decrypting: authentication failed.',
    'encryptionFailed'     => 'Encryption failed.',
];
